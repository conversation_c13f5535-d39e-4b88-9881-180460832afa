import 'dart:io';

// import 'package:android_intent_plus/android_intent.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:instsaver/Utility/Constant.dart';
import 'package:instsaver/Views/Screen/MainScreen.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import 'Modal/DbModal.dart';
import 'Services/InstaDbServices.dart';
import 'Services/NotificationServices.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize in-app purchase
  InAppPurchase.instance;

  await Hive.initFlutter();
  await Permission.notification.request();
  await Permission.videos.request();
  await Permission.photos.request();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  await InstaDbServices.instaDbServices.initDB();
  checkFileAndDelete();
  AndroidInitializationSettings initializationSettingsAndroid =
      const AndroidInitializationSettings('@mipmap/ic_launcher');
  DarwinInitializationSettings initializationSettingsIOS =
      const DarwinInitializationSettings();

  InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );
  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    '1', // id
    'Simple Notification', // name
    description:
        'This channel is used for simple notifications.', // description
    importance: Importance.max,
  );

  _fetchApplicationData();

  await NotificationServices.flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  await NotificationServices.flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: onSelectNotification,
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'InstSaver : Reels Downloader',
      theme: ThemeData(
        appBarTheme: const AppBarTheme(
          backgroundColor: AppColor.primaryColor,
        ),
        colorScheme: ColorScheme.fromSeed(seedColor: AppColor.primaryColor),
        useMaterial3: true,
      ),
      home:  const MainScreen(),
    );
  }
}

Future<void> onSelectNotification(
    NotificationResponse notificationResponse) async {
  final String? payload = notificationResponse.payload;
  if (payload != null) {
    payload.contains('mp4') ? await openvideo(payload) : await _openGallery();
  }
}

Future<void> _openGallery() async {
  final Uri galleryUri = Uri.parse('content://media/internal/images/media');
  if (await canLaunchUrl(galleryUri)) {
    await launchUrl(galleryUri);
  } else {
    throw 'Could not launch $galleryUri';
  }
}

Future<void> openvideo(String? path) async {
  // if (path == null) return;
  // AndroidIntent intent = AndroidIntent(
  //   action: 'android.intent.action.VIEW',
  //   data: path,
  //   type: path.contains('.mp4') ? 'video/mp4' : 'image/*',
  // );

  // await intent.launch();
}

Future<void> checkFileAndDelete() async {
  List<InstaDbModal> postlist =
      await InstaDbServices.instaDbServices.fetchPost();
  for (var post in postlist) {
    final String videoPath = post.filepath ?? '';
    final int postId = post.id ?? 0;

    final file = File(videoPath);

    if (await file.exists()) {
      // If file does not exist, delete the record from the database
    } else {
      await InstaDbServices.instaDbServices.deletePost(postId);
    }
  }
}
void _fetchApplicationData() async {
  var dio = Dio();
  try {
    Response response = await dio.get(
      ApplicationURLS.application_url,
      options: Options(
        receiveTimeout: Duration(seconds: 30), // Longer timeout
      ),
    );

    if (response.statusCode == 200) {
      var jsonResponse = response.data;
      ApplicationData.application_name =
          jsonResponse['application_name'] ?? ApplicationData.application_name;
      ApplicationData.application_description =
          jsonResponse['application_description'] ??
              ApplicationData.application_description;
      ApplicationData.application_url =
          jsonResponse['application_url'] ?? ApplicationData.application_url;
      ApplicationData.is_ads = jsonResponse['is_ads'] ?? ApplicationData.is_ads;
      ApplicationData.is_native_ads =
          jsonResponse['is_native_ads'] ?? ApplicationData.is_native_ads;
      ApplicationData.is_ad_open =
          jsonResponse['is_ad_open'] ?? ApplicationData.is_ad_open;
      ApplicationData.is_banne_ad =
          jsonResponse['is_banne_ad'] ?? ApplicationData.is_banne_ad;
    } else {
      print("HTTP Error: ${response.statusCode}");
    }
  } catch (error) {
    print("Request Failed: $error");
  }
}
